/* eslint-disable max-lines */
import {memo, MouseEvent, useCallback, useMemo, useRef, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Flex, Divider, Typography, Tooltip, Dropdown, MenuProps, Popconfirm} from 'antd';
import {Modal, message} from '@panda-design/components';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {useBoolean} from 'huse';
import {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase, MCPEditTab, ApplicationBase} from '@/types/mcp/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MCPC<PERSON> from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import UpdateInfo from '@/components/MCP/UpdateInfo';
import SvgEye from '@/icons/mcp/Eye';
import SvgCallCount from '@/icons/mcp/CallCount';
import SvgVip from '@/icons/mcp/Vip';
import {colors} from '@/constants/colors';
import {IconDetail, IconMore, IconSubscribe2, IconAlert} from '@/icons/mcp';
import {apiDeleteServer} from '@/api/mcp';
import {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';
import {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';

const containerCss = css`
    padding: 17px 24px 12px;
    position: relative;
    border-radius: 6px;
    z-index: 1;
    &:hover {
        border-color: #0080FF;
        .hover-actions {
            opacity: 1;
            min-height: 51px;
            padding: 0 24px 20px;
        }
    }
`;

const DescriptionContainer = styled.div`
    margin: 17px 0 12px;
    font-size: 14px;
    line-height: 22px;
    position: relative;
    height: 44px;
    color: #545454;
    overflow: hidden;
`;

const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
    overflow: hidden;
`;

const EllipsisOverlay = styled.div`
    position: absolute;
    bottom: 9px;
    right: 12px;
    padding-left: 10px;
    pointer-events: none;
`;

const cardContentStyle = {
    overflow: 'hidden',
    flex: 1,
};

const protocolTextStyle = {
    color: '#8F8F8F',
    fontSize: 12,
    lineHeight: '18px',
};

const dividerStyle = {
    margin: '16px 0 8px',
};

const statsContainerStyle = css`
    color: ${colors['gray-7']};
    font-size: 12px;
    line-height: 20px;
    transition: color 0.2s ease;
    cursor: pointer;
    &:hover {
        color: ${colors.primary};
    }
`;

const iconStyle = {
    width: 14,
    height: 14,
};

const actionButtonStyle = css`
    display: flex;
    align-items: center;
    gap: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: ${colors['gray-7']};
    font-size: 12px;
    line-height: 20px;
    transition: all 0.2s ease;
    &:hover {
        background-color: rgba(0, 128, 255, 0.1);
    }
`;

const moreButtonStyle = css`
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    color: ${colors['gray-7']};
    transition: all 0.2s ease;
    &:hover {
        background-color: rgba(0, 128, 255, 0.1);
    }
`;

const formatCount = (count: number): string => {
    if (count >= 10000) {
        return `${Math.floor(count / 10000)}w+`;
    }
    if (count >= 1000) {
        return `${Math.floor(count / 1000)}k+`;
    }
    return count.toString();
};

const formatProtocolType = (protocolType: string): string => {
    if (protocolType === 'Streamable_HTTP') {
        return 'Streamable HTTP';
    }
    return protocolType;
};

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();
    const descriptionRef = useRef<HTMLDivElement>(null);
    const [isDescriptionOverflowing, setIsDescriptionOverflowing] = useState(false);
    const [open, {on, off}] = useBoolean(false);
    const appList = useAppListForMCPServer(server.id);

    const apps = useMemo<ApplicationBase[]>(
        () => {
            const data = appList?.reduce((acc, cur) => {
                return [...acc, ...(spaceId && cur.workspaceId !== spaceId ? [] : cur.applications)];
            }, []);
            return data;
        },
        [appList, spaceId]
    );

    useEffect(
        () => {
            const checkOverflow = () => {
                if (descriptionRef.current) {
                    const element = descriptionRef.current;
                    const isOverflowing = element.scrollHeight > element.clientHeight;
                    setIsDescriptionOverflowing(isOverflowing);
                }
            };

            checkOverflow();
            window.addEventListener('resize', checkOverflow);
            return () => window.removeEventListener('resize', checkOverflow);
        },
        [server.description]
    );

    useEffect(
        () => {
            if (!spaceId && !apps?.length) {
                loadAppListForMCPServer({mcpServerId: server.id});
            }
            if (spaceId) {
                loadAppListForMCPServer({mcpServerId: server.id});
            }
        },
        [apps?.length, server.id, spaceId]
    );

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: MCPEditTab.Tools}));
        },
        [navigate, spaceId, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview', workspaceId: spaceId});
            navigate(url);
        },
        [navigate, server.id, spaceId]
    );

    const handleUseCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'tools', workspaceId: spaceId});
            navigate(url);
        },
        [navigate, server.id, spaceId]
    );

    const handleDetailClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview', workspaceId: spaceId});
            navigate(url);
        },
        [navigate, server.id, spaceId]
    );

    const handleSubscribeClick = useCallback(
        async (e: MouseEvent) => {
            e.stopPropagation();
            try {
                if (apps?.length) {
                    setMCPSubscribe({
                        serverId: server.id,
                        workspaceId: spaceId,
                        enableGlobalParams: false,
                        onSuccess: () => loadAppListForMCPServer({mcpServerId: server.id}),
                    });
                }
                else {
                    on();
                }
            }
            catch (e) {
                message.error('服务器开小差了，请稍后重试');
            }
        },
        [apps, server.id, spaceId, on]
    );

    const handlePopconfirmCancel = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            off();
        },
        [off]
    );

    const handlePopconfirmOk = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            off();
            openMCPApplicationModal();
        },
        [off]
    );

    const handleDropdownClick: MenuProps['onClick'] = useCallback(
        ({key, domEvent}) => {
            domEvent?.stopPropagation();
            if (key === 'config') {
                navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: MCPEditTab.Tools}));
            } else if (key === 'delete') {
                Modal.confirm({
                    content: 'MCP Server删除后不可恢复，请谨慎操作。确定删除当前MCP Server吗？',
                    icon: <IconAlert />,
                    onOk: async () => {
                        await apiDeleteServer({mcpServerId: server.id});
                        message.success('删除成功');
                        refresh();
                    },
                });
            }
        },
        [navigate, spaceId, server.id, refresh]
    );

    const tags = useMemo(
        () => (server.labels ?? []).map((label, index) => ({
            id: label.id || index,
            label: label.labelValue,
        })),
        [server.labels]
    );

    const moreMenuItems: MenuProps['items'] = [
        {
            key: 'config',
            label: '工具配置',
        },
        {
            type: 'divider',
        },
        {
            key: 'delete',
            label: '删除',
        },
    ];

    return (
        <div>
            <MCPCard
                vertical
                onClick={handleClick}
                className={`mcp-card ${containerCss}`}
                official={server.official}
            >
                <Flex gap={16} align="center">
                    <div style={{position: 'relative', display: 'inline-block'}}>
                        <MCPServerAvatar
                            icon={server.icon}
                            style={server.official ? {
                                border: '2px solid',
                                borderImageSource:
                                    'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',
                                borderImageSlice: 1,
                            } : undefined}
                        />
                        {server.official && (
                            <SvgVip
                                style={{
                                    position: 'absolute',
                                    bottom: -7,
                                    right: -4,
                                    fontSize: '23px',
                                }}
                            />
                        )}
                    </div>
                    <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                        <Typography.Title level={4} ellipsis style={{color: '#181818'}}>
                            {server.name}
                        </Typography.Title>
                        <Flex align="center" gap={4}>
                            <Typography.Text style={protocolTextStyle}>
                                {getServerTypeText(server.serverProtocolType)}
                            </Typography.Text>
                            <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                            <Typography.Text style={protocolTextStyle}>
                                {formatProtocolType(server.serverProtocolType)}
                            </Typography.Text>
                        </Flex>
                    </Flex>
                </Flex>
                {server.description && isDescriptionOverflowing ? (
                    <Tooltip title={server.description} placement="top">
                        <DescriptionContainer ref={descriptionRef}>
                            <DescriptionText>{server.description}</DescriptionText>
                            <EllipsisOverlay />
                        </DescriptionContainer>
                    </Tooltip>
                ) : (
                    <DescriptionContainer ref={descriptionRef}>
                        <DescriptionText>{server.description || '暂无描述'}</DescriptionText>
                        <EllipsisOverlay />
                    </DescriptionContainer>
                )}
                <TagGroup
                    labels={tags}
                    color="light-purple"
                    prefix={null}
                    style={{flexShrink: 1, overflow: 'hidden'}}
                    gap={4}
                />
                <div style={{marginTop: 16}}>
                    <UpdateInfo
                        username={server.lastModifyUser}
                        time={server.lastModifyTime}
                        showAvatar={false}
                        color={'#000'}
                    />
                </div>
                <Divider style={dividerStyle} />
                <Flex justify="space-between" align="center">
                    <Flex align="center" gap={12}>
                        <Tooltip title="浏览量">
                            <Flex
                                align="center"
                                gap={4}
                                onClick={handleViewCountClick}
                                className={statsContainerStyle}
                            >
                                <SvgEye style={iconStyle} />
                                {formatCount(server.serverMetrics?.viewCount || 0)}
                            </Flex>
                        </Tooltip>
                        <Tooltip title="调用量">
                            <Flex
                                align="center"
                                gap={4}
                                className={statsContainerStyle}
                                onClick={handleUseCountClick}
                            >
                                <SvgCallCount style={iconStyle} />
                                {formatCount(server.serverMetrics?.callCount || 0)}
                            </Flex>
                        </Tooltip>
                    </Flex>
                    <Flex align="center" gap={15}>
                        <div className={actionButtonStyle} onClick={handleDetailClick}>
                            <IconDetail style={iconStyle} />
                            详情
                        </div>
                        <Popconfirm
                            title=""
                            description="您还没有可用应用，请先创建后订阅"
                            open={open}
                            onConfirm={handlePopconfirmOk}
                            onCancel={handlePopconfirmCancel}
                            okText="立即创建"
                            cancelText="稍后再说"
                            placement="bottom"
                        >
                            <div className={actionButtonStyle} onClick={handleSubscribeClick}>
                                <IconSubscribe2 style={iconStyle} />
                                订阅
                            </div>
                        </Popconfirm>
                        <Dropdown
                            menu={{items: moreMenuItems, onClick: handleDropdownClick}}
                            trigger={['hover']}
                            placement="bottomRight"
                        >
                            <div className={moreButtonStyle}>
                                <IconMore style={iconStyle} />
                            </div>
                        </Dropdown>
                    </Flex>
                </Flex>
            </MCPCard>
            <div
                className="mcp-release-status"
                style={{
                    position: 'absolute',
                    top: 1,
                    right: 7,
                    zIndex: 3,
                }}
            >
                <MCPReleaseStatus
                    status={server.serverStatus}
                    publishType={server.serverPublishType}
                />
            </div>
        </div>
    );
};

export default memo(SpaceMCPCard);
